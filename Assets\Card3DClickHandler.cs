using UnityEngine;
using UnityEngine.EventSystems;

public class Card3DClickHandler : MonoBehaviour
{
    private CardData cardData;
    private int cardIndex = -1;
    
    public void Initialize(CardData data, int index = -1)
    {
        cardData = data;
        cardIndex = index;
    }

    private void OnMouseUp()
    {
        // Check if mouse is over specific UI elements
        if (IsMouseOverSpecificUI())
        {
            return;
        }

        // Check if this click should be suppressed
        if (AssemblerHandler.ShouldSuppressCardClick() || TechBuildHandler.ShouldSuppressCardClick())
        {
            Debug.Log("Card click suppressed for build mode");
            return;
        }

        CardRowIndex rowIndex = GetComponent<CardRowIndex>();
        if (rowIndex != null && rowIndex.Index >= 0)
        {
            cardIndex = rowIndex.Index;
        }

        if (cardData != null)
        {
            CardDetailDisplay.CardSource cardSource = GetCardSource();
            int ownerId = GetOwnerPlayerId(cardSource);

            if (CardDetailDisplay.Instance != null)
            {
                CardDetailDisplay.Instance.ShowCard(cardData, cardIndex, cardSource, gameObject, ownerId);
            }
            else
            {
                Debug.LogError("Card3DClickHandler: CardDetailDisplay.Instance is null!");
            }
        }
        else
        {
            Debug.LogError("Card3DClickHandler: cardData is null!");
        }
    }

    private bool IsMouseOverSpecificUI()
    {
        Vector2 mousePos = Input.mousePosition;

        // Check detail panel
        CardDetailDisplay detailDisplay = CardDetailDisplay.Instance;
        if (detailDisplay != null)
        {
            GameObject detailPanel = detailDisplay.GetDetailPanel();
            if (detailPanel != null && detailPanel.activeInHierarchy)
            {
                RectTransform panelRect = detailPanel.GetComponent<RectTransform>();
                if (panelRect != null && RectTransformUtility.RectangleContainsScreenPoint(panelRect, mousePos))
                {
                    return true;
                }
            }
        }

        // Check log panel
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            // Use reflection to access private field
            System.Reflection.FieldInfo logPanelField = typeof(GameLogManager).GetField("logPanelTransform",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (logPanelField != null)
            {
                RectTransform logPanelRect = logPanelField.GetValue(logManager) as RectTransform;
                if (logPanelRect != null && RectTransformUtility.RectangleContainsScreenPoint(logPanelRect, mousePos))
                {
                    return true;
                }
            }
        }

        return false;
    }


    private CardDetailDisplay.CardSource GetCardSource()
    {
        // Default is card row
        CardDetailDisplay.CardSource source = CardDetailDisplay.CardSource.CardRow;

        // Check if this card is in a player's play area
        Transform parent = transform.parent;
        while (parent != null)
        {
            // Check for technology deck manager (card row)
            if (parent.GetComponent<TechnologyDeckManager>() != null ||
                parent.name.Contains("CardRow"))
            {
                source = CardDetailDisplay.CardSource.CardRow;
                break;
            }

            // Check for player play area
            if (parent.GetComponent<PlayerPlayArea>() != null)
            {
                // Determine if it's in hand or play area
                if (parent.name.Contains("Hand") || parent.name.Contains("TechnologyCards"))
                {
                    source = CardDetailDisplay.CardSource.PlayerHand;
                }
                else
                {
                    source = CardDetailDisplay.CardSource.PlayerPlayArea;
                }
                break;
            }

            // Check for world card
            if (parent.GetComponent<WorldCardVisual>() != null)
            {
                source = CardDetailDisplay.CardSource.WorldCard;
                break;
            }

            // Move up the hierarchy
            parent = parent.parent;
        }

        return source;
    }

    private int GetOwnerPlayerId(CardDetailDisplay.CardSource cardSource)
    {
        // Only return player ID for cards in play areas (constructed ships/modules/facilities/wonders)
        if (cardSource == CardDetailDisplay.CardSource.PlayerPlayArea)
        {
            // Traverse up the hierarchy to find the PlayerPlayArea component
            Transform parent = transform.parent;
            while (parent != null)
            {
                PlayerPlayArea playArea = parent.GetComponent<PlayerPlayArea>();
                if (playArea != null && playArea.player != null)
                {
                    return playArea.player.PlayerId;
                }
                parent = parent.parent;
            }
        }
        
        // Return -1 for non-play area cards (technology deck, hand, etc.)
        return -1;
    }
}