using UnityEngine;
using System.Collections.Generic;

public class ModulePieceClickHandler : MonoBehaviour
{
    private int playerId;
    private GameObject world;
    
    public void Initialize(int playerIndex, GameObject worldLocation)
    {
        playerId = playerIndex;
        world = worldLocation;
    }

    private void OnMouseUp()
    {
        // Check if mouse is over specific UI elements
        if (IsMouseOverSpecificUI())
        {
            return;
        }

        // Check if this click should be suppressed
        if (AssemblerHandler.ShouldSuppressCardClick() || TechBuildHandler.ShouldSuppressCardClick())
        {
            Debug.Log("Module piece click suppressed for build mode");
            return;
        }

        // Get the player's modules at this location
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null && playerId >= 0 && playerId < gameManager.Players.Count && world != null)
        {
            Player player = gameManager.Players[playerId];
            List<Module> modulesAtLocation = player.GetModulesOnPlanet(world);
            
            if (modulesAtLocation.Count > 0)
            {
                if (CardDetailDisplay.Instance != null)
                {
                    // Show the entire module collection instead of just one module
                    CardDetailDisplay.Instance.ShowModuleCollection(modulesAtLocation, playerId, gameObject, world);
                }
                else
                {
                    Debug.LogError("ModulePieceClickHandler: CardDetailDisplay.Instance is null!");
                }
            }
            else
            {
                Debug.LogWarning($"ModulePieceClickHandler: No modules found for player {playerId} at {world.name}");
            }
        }
        else
        {
            Debug.LogError("ModulePieceClickHandler: Invalid player ID or world location!");
        }
    }


    private bool IsMouseOverSpecificUI()
    {
        Vector2 mousePos = Input.mousePosition;

        // Check detail panel
        CardDetailDisplay detailDisplay = CardDetailDisplay.Instance;
        if (detailDisplay != null)
        {
            GameObject detailPanel = detailDisplay.GetDetailPanel();
            if (detailPanel != null && detailPanel.activeInHierarchy)
            {
                RectTransform panelRect = detailPanel.GetComponent<RectTransform>();
                if (panelRect != null && RectTransformUtility.RectangleContainsScreenPoint(panelRect, mousePos))
                {
                    return true;
                }
            }
        }

        // Check log panel
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            // Use reflection to access private field
            System.Reflection.FieldInfo logPanelField = typeof(GameLogManager).GetField("logPanelTransform",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (logPanelField != null)
            {
                RectTransform logPanelRect = logPanelField.GetValue(logManager) as RectTransform;
                if (logPanelRect != null && RectTransformUtility.RectangleContainsScreenPoint(logPanelRect, mousePos))
                {
                    return true;
                }
            }
        }

        return false;
    }
}